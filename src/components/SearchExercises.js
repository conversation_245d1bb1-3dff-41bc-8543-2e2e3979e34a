import React, { useEffect, useState } from 'react'
import { Box, Button, Stack, TextField, Typography } from '@mui/material'
import { textTransform } from '@mui/system'

import { exercisesOptions, fetchData } from '../utils/fetchData'
import { searchExercises } from '../utils/mockData'
import HorizontalScrollbar from './HorizontalScrollbar'

const SearchExercises = ({ exercises,setExercises, bodyPart, setBodyPart }) => {
    const [search, setSearch] = useState('')
    const [bodyParts, setBodyParts] = useState([]);
    // var c = 1;

    useEffect(() => {
        const fetchExercisesData = async () => {
            try {
                const bodyPartsData = await fetchData('https://exercisedb.p.rapidapi.com/exercises/bodyPartList'
                    , exercisesOptions);

                console.log('bodyPartsData:', bodyPartsData);

                // Check if bodyPartsData is an array before spreading
                if (Array.isArray(bodyPartsData)) {
                    setBodyParts(['all', ...bodyPartsData]);
                } else {
                    console.error('bodyPartsData is not an array:', bodyPartsData);
                    // Set a default list of body parts if API fails
                    setBodyParts(['all', 'back', 'cardio', 'chest', 'lower arms', 'lower legs', 'neck', 'shoulders', 'upper arms', 'upper legs', 'waist']);
                }
            } catch (error) {
                console.error('Error fetching body parts:', error);
                // Set a default list of body parts if API fails
                setBodyParts(['all', 'back', 'cardio', 'chest', 'lower arms', 'lower legs', 'neck', 'shoulders', 'upper arms', 'upper legs', 'waist']);
            }
        }
        fetchExercisesData();
    }, [])

    const handelSearch = async () => {
        if (search) {
            try {
                const exercisesData = await fetchData
                    ('https://exercisedb.p.rapidapi.com/exercises', exercisesOptions);
                console.log('exercisesData:', exercisesData);

                // Check if exercisesData is an array before filtering
                if (Array.isArray(exercisesData)) {
                    const searchResults = exercisesData.filter(
                        (exercise) => exercise.name.toLowerCase().includes(search)
                            || exercise.target.toLowerCase().includes(search)
                            || exercise.equipment.toLowerCase().includes(search)
                            || exercise.bodyPart.toLowerCase().includes(search)
                    );
                    setSearch('');
                    setExercises(searchResults);
                } else {
                    console.log('Using mock search data');
                    // Use mock search when API data is not available
                    const searchResults = searchExercises(search);
                    setSearch('');
                    setExercises(searchResults);
                }
            } catch (error) {
                console.error('Error fetching exercises, using mock search:', error);
                const searchResults = searchExercises(search);
                setSearch('');
                setExercises(searchResults);
            }
        }
    }
    useEffect(()=>{
        window.scrollTo({ top: 1800, left: 100, behavior: 'smooth' })
    },[exercises])

    return (
        <Stack alignItems="center" mt='37px'
            justifyContent="center" p='20px'>
            <Typography fontWeight={700}
                sx={{ fontSize: { lg: '44px', xs: '30px' } }}
                mb="50px" textAlign="center">
                Awesome Exercises You <br />
                Should Know
            </Typography>
            <Box position="relative" mb='72px'>
                <TextField
                    sx={{
                        input: {
                            fontWeight: '700px', border: 'none',
                            borderRadius: '4px'
                        },
                        width: { lg: '800px', xs: '350px' },
                        backgroundColor: '#fff',
                        borderRadius: '40px'
                    }}
                    height="76px"
                    value={search}
                    onChange={(e) => {
                        setSearch(e.target.value
                            .toLowerCase())
                    }}
                    placeholder="Search Exercises"
                    type='text'
                />
                <Button className='search Exercises'
                    sx={{
                        bgcolor: '#FF2625',
                        color: '#fff',
                        textTransform: 'none',
                        width: { lg: '175px', xs: '80px' },
                        fontSize: { lg: '20px', xs: '14px' },
                        height: '56px',
                        position: "absolute",
                        right: '0'
                    }}
                    onClick={handelSearch}
                >
                    Search
                </Button>
            </Box>
            <Box sx={{ position: 'relative', width: '100%', p: '20px' }}>
                <HorizontalScrollbar data={bodyParts}
                    bodyPart={bodyPart}
                    setBodyPart={setBodyPart}
                    isBodyParts
                />
            </Box>
        </Stack>
    )
}

export default SearchExercises
