
import { mockBodyParts, mockExercises, getExercisesByBodyPart, searchExercises } from './mockData';

export const exercisesOptions = {
    method: 'GET',
    headers: {
        'X-RapidAPI-Key': '**************************************************',
        'X-RapidAPI-Host': 'exercisedb.p.rapidapi.com'
    }
};

export const youtubeOptions = {
    method: 'GET',
    headers: {
        'X-RapidAPI-Key': '**************************************************',
        'X-RapidAPI-Host': 'youtube-search-and-download.p.rapidapi.com'
    }
};

// Helper function to extract body part from URL
const getBodyPartFromUrl = (url) => {
    const match = url.match(/bodyPart\/([^/?]+)/);
    return match ? match[1] : null;
};

export const fetchData = async (url, option) => {
    try {
        const response = await fetch(url, option);
        const data = await response.json();

        // Check if API returned an error (like blocked user)
        if (data.message && data.message.includes('Blocked User')) {
            console.warn('API blocked, using mock data instead');
            return getMockData(url);
        }

        return data;
    } catch (error) {
        console.warn('API request failed, using mock data instead:', error);
        return getMockData(url);
    }
};

// Function to return appropriate mock data based on URL
const getMockData = (url) => {
    if (url.includes('bodyPartList')) {
        return mockBodyParts.slice(1); // Remove 'all' as it's added separately
    } else if (url.includes('bodyPart/')) {
        const bodyPart = getBodyPartFromUrl(url);
        return getExercisesByBodyPart(bodyPart);
    } else if (url.includes('exercises')) {
        return mockExercises;
    }
    return [];
};