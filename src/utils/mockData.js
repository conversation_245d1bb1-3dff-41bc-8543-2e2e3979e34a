// Mock data for development when API is not available
export const mockBodyParts = [
    'all',
    'back',
    'cardio',
    'chest',
    'lower arms',
    'lower legs',
    'neck',
    'shoulders',
    'upper arms',
    'upper legs',
    'waist'
];

export const mockExercises = [
    {
        id: '1',
        name: 'Push-ups',
        target: 'pectorals',
        equipment: 'body weight',
        bodyPart: 'chest',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzE1',
        instructions: [
            'Start in a plank position with your hands slightly wider than shoulder-width apart.',
            'Lower your body until your chest nearly touches the floor.',
            'Push yourself back up to the starting position.',
            'Repeat for the desired number of repetitions.'
        ]
    },
    {
        id: '2',
        name: 'Squats',
        target: 'quadriceps',
        equipment: 'body weight',
        bodyPart: 'upper legs',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzE2',
        instructions: [
            'Stand with feet shoulder-width apart.',
            'Lower your body as if sitting back into a chair.',
            'Keep your chest up and knees behind your toes.',
            'Return to standing position.'
        ]
    },
    {
        id: '3',
        name: 'Pull-ups',
        target: 'latissimus dorsi',
        equipment: 'pull-up bar',
        bodyPart: 'back',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzE3',
        instructions: [
            'Hang from a pull-up bar with palms facing away.',
            'Pull your body up until your chin clears the bar.',
            'Lower yourself back to the starting position.',
            'Repeat for desired repetitions.'
        ]
    },
    {
        id: '4',
        name: 'Shoulder Press',
        target: 'deltoids',
        equipment: 'dumbbell',
        bodyPart: 'shoulders',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzE4',
        instructions: [
            'Stand with feet shoulder-width apart, holding dumbbells at shoulder height.',
            'Press the weights overhead until arms are fully extended.',
            'Lower the weights back to shoulder height.',
            'Repeat for desired repetitions.'
        ]
    },
    {
        id: '5',
        name: 'Bicep Curls',
        target: 'biceps',
        equipment: 'dumbbell',
        bodyPart: 'upper arms',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzE5',
        instructions: [
            'Stand with feet shoulder-width apart, holding dumbbells at your sides.',
            'Curl the weights up towards your shoulders.',
            'Squeeze your biceps at the top of the movement.',
            'Lower the weights back to the starting position.'
        ]
    },
    {
        id: '6',
        name: 'Calf Raises',
        target: 'calves',
        equipment: 'body weight',
        bodyPart: 'lower legs',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzIw',
        instructions: [
            'Stand with feet shoulder-width apart.',
            'Rise up onto your toes as high as possible.',
            'Hold for a moment at the top.',
            'Lower back down to the starting position.'
        ]
    },
    {
        id: '7',
        name: 'Plank',
        target: 'core',
        equipment: 'body weight',
        bodyPart: 'waist',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzIx',
        instructions: [
            'Start in a push-up position.',
            'Lower onto your forearms.',
            'Keep your body in a straight line from head to heels.',
            'Hold this position for the desired time.'
        ]
    },
    {
        id: '8',
        name: 'Jumping Jacks',
        target: 'cardiovascular system',
        equipment: 'body weight',
        bodyPart: 'cardio',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzIy',
        instructions: [
            'Stand with feet together and arms at your sides.',
            'Jump while spreading your legs shoulder-width apart and raising your arms overhead.',
            'Jump back to the starting position.',
            'Repeat at a steady pace.'
        ]
    },
    {
        id: '9',
        name: 'Tricep Dips',
        target: 'triceps',
        equipment: 'body weight',
        bodyPart: 'upper arms',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzIz',
        instructions: [
            'Sit on the edge of a chair or bench.',
            'Place your hands beside your hips and slide forward.',
            'Lower your body by bending your elbows.',
            'Push back up to the starting position.'
        ]
    },
    {
        id: '10',
        name: 'Neck Rolls',
        target: 'neck muscles',
        equipment: 'body weight',
        bodyPart: 'neck',
        gifUrl: 'https://v2.exercisedb.io/image/3RlZjNkYzJhNzI0',
        instructions: [
            'Stand or sit with good posture.',
            'Slowly roll your head in a circular motion.',
            'Complete circles in one direction, then reverse.',
            'Keep movements slow and controlled.'
        ]
    }
];

// Function to filter exercises by body part
export const getExercisesByBodyPart = (bodyPart) => {
    if (bodyPart === 'all') {
        return mockExercises;
    }
    return mockExercises.filter(exercise => exercise.bodyPart === bodyPart);
};

// Function to search exercises
export const searchExercises = (searchTerm) => {
    const term = searchTerm.toLowerCase();
    return mockExercises.filter(exercise => 
        exercise.name.toLowerCase().includes(term) ||
        exercise.target.toLowerCase().includes(term) ||
        exercise.equipment.toLowerCase().includes(term) ||
        exercise.bodyPart.toLowerCase().includes(term)
    );
};
