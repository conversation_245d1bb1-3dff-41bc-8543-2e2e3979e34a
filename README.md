# Gym Exercises website
I have build this website using reactJS and used rapid api for getting different exercises details and their gifs.
I have used [ExerciseDb](https://rapidapi.com/justin-WFnsXH_t6/api/exercisedb/) and [youtube search and download](https://rapidapi.com/h0p3rwe/api/youtube-search-and-download/) api for getting data.

## About site
On this website you can search for any exercise related things like exercise name or equipment used or any muscle exercise you want to do.
Then from that search you will get multiple results from ExerciseDb api and they will be shown below.
If you click on any exercise card then it will show you detail about that exercise and youtube videos that shows how to do it.
Also It show's exercises that target same muscle group and also exercises that use same equipment.
It's live hosted on netlify.

[Live Hosted Link](https://golds-gym-up1512001.netlify.app/)

# Images

### Basic site view
![](./src/assets/images/ss1%20(1).png)

### Search view
![](./src/assets/images/ss1%20(2).png)

### Card shows different exercises
![](./src/assets/images/ss1%20(3).png)

### Detail about selected exercise
![](./src/assets/images/ss1%20(4).png)

### Related youtube videos
![](./src/assets/images/ss1%20(5).png)

### Exercises target same muscle and uses same equipment
![](./src/assets/images/ss1%20(6).png)
